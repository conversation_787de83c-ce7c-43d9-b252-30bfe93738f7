
// 获取安装按钮
//const installButton = document.getElementById('InstallBtn');
//console.log('installButton ' + installButton);

let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
});

installButton.addEventListener('click', (e) => {
	console.log('installButtonclick');
    deferredPrompt.prompt();
    deferredPrompt.userChoice.then((choiceResult) => {
        if (choiceResult.outcome === 'accepted') {
			console.log('User accepted the install prompt');
                
		} else {
			console.log('User dismissed the install prompt');
		}
		deferredPrompt = null;
	});
        
});


window.installButtonclick = function(){
	console.log('installButtonclick');

	// 检查 deferredPrompt 是否存在
	if (!deferredPrompt) {
		console.log("PWA install prompt not available");

		// 检查是否已经安装
		if (window.matchMedia('(display-mode: standalone)').matches) {
			alert("应用已经安装！");
			return;
		}

		// 显示手动安装指引
		const userAgent = navigator.userAgent;
		let message = "";

		if (userAgent.indexOf("Chrome") > -1) {
			message = "请点击浏览器地址栏右侧的安装图标来安装应用";
		} else if (userAgent.indexOf("Edge") > -1) {
			message = "请点击浏览器地址栏右侧的应用图标来安装应用";
		} else if (userAgent.indexOf("Firefox") > -1) {
			message = "Firefox 浏览器暂不支持 PWA 安装，请使用 Chrome 或 Edge 浏览器";
		} else if (userAgent.indexOf("Safari") > -1) {
			message = "Safari 浏览器暂不支持 PWA 安装，请使用 Chrome 或 Edge 浏览器";
		} else {
			message = "当前浏览器可能不支持 PWA 安装，建议使用 Chrome 或 Edge 浏览器";
		}

		alert(message);
		return;
	}

	// 如果 deferredPrompt 存在，执行安装
    deferredPrompt.prompt();
    deferredPrompt.userChoice.then((choiceResult) => {
        if (choiceResult.outcome === 'accepted') {
			console.log('User accepted the install prompt');

		} else {
			console.log('User dismissed the install prompt');
		}
		deferredPrompt = null;
	});

}


window.addEventListener('appinstalled', (evt) => {
    window.open('/index.html', '_self');
});
