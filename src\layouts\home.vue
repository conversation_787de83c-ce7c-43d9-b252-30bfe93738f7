<script setup lang="ts" name="home">
import AppGameJackpotNew from "~/components/AppGameJackpotNew.vue";

const appStore = useAppStore();
const { isLogin } = storeToRefs(appStore);

if (isLogin) {
  appStore.runGetMemberInfo();
}

appStore.setFooterDialogVisble(true);

onMounted(() => {
  const dom: any = document.querySelector(".app-index");

  dom.addEventListener("scroll", () => {
    if (dom.scrollTop > 200) {
      (document.querySelector(".app-index-go-top") as any).style.display =
        "flex";
    } else {
      (document.querySelector(".app-index-go-top") as any).style.display =
        "none";
    }
  });
});
</script>

<template>
  <div class="app-index">
    <AppIndexHeader />
    <AppBanner class="app-banner-border" :list-data="[]" />
    //下载按钮
    <AppMiniBanner />
    <!-- <AppGameJackpot /> -->

    <!-- <AppGameMarquee /> -->
    <AppSpeaker />
    <AppScrollTab />
    <!--走马灯 -->
    <!-- <AppGameTab /> -->
    <!--类型选择-->
    <!-- <AppIndexMenu /> -->

    <!-- <AppGameContainer gameType="1" /> -->
    <div v-for="n in 7">
      <!-- {{  }} -->
      <AppGameContainer :gameType="n - 1" />
    </div>

    <div class="app-index-container">
      <RouterView />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.app-index {
  // background-color: aqua;
  // padding-top: 110px;
  // @include webp('/icons/bg_pattern_tile');
  // background-image: url("/img/index/bg_pattern_tile.webp");
  // background-repeat: repeat;
  // background-size: 232px 232px;

  position: relative;
  overflow-x: hidden !important;
  overflow-y: auto;
}

.app-index-container {
  padding: 0px 20px 00px 20px;
}
</style>
