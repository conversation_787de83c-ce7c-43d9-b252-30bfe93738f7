<script setup lang="ts" name="home">
import AppGameJackpotNew from "~/components/AppGameJackpotNew.vue";

// 扩展 Window 接口
declare global {
  interface Window {
    deferredPrompt?: any;
    installButtonclick?: () => void;
  }
}

const appStore = useAppStore();
const { isLogin } = storeToRefs(appStore);

if (isLogin) {
  appStore.runGetMemberInfo();
}

appStore.setFooterDialogVisble(true);

// PWA 安装相关
const showInstallButton = ref(false);
const userAgent = navigator.userAgent || (navigator as any).vendor || '';

// 检测设备类型
const isIOS = userAgent.indexOf("iPad") > -1 || userAgent.indexOf("iPhone") > -1 || userAgent.indexOf("iPod") > -1;
const isAndroid = userAgent.indexOf("Android") > -1;
const isDesktop = !isIOS && !isAndroid;

// 根据设备类型显示不同的文本
const installButtonText = computed(() => {
  if (isIOS) {
    return {
      title: "安装应用",
      subtitle: "添加到主屏幕，获得更好体验"
    };
  } else if (isAndroid) {
    return {
      title: "安装应用",
      subtitle: "添加到主屏幕，获得更好体验"
    };
  } else {
    return {
      title: "安装应用",
      subtitle: "安装到电脑，获得更好体验"
    };
  }
});

onMounted(() => {
  const dom: any = document.querySelector(".app-index");

  dom.addEventListener("scroll", () => {
    if (dom.scrollTop > 200) {
      (document.querySelector(".app-index-go-top") as any).style.display =
        "flex";
    } else {
      (document.querySelector(".app-index-go-top") as any).style.display =
        "none";
    }
  });

  // 检查是否显示安装按钮
  checkInstallAvailability();
});

// 检查安装可用性
const checkInstallAvailability = () => {
  // 如果已经是 PWA 模式，不显示安装按钮
  if (window.matchMedia('(display-mode: standalone)').matches) {
    showInstallButton.value = false;
    return;
  }

  // 始终显示安装按钮（支持所有平台包括电脑端）
  showInstallButton.value = true;

  // 可选：如果想要更精确的检测，可以检查是否有 beforeinstallprompt 事件
  // 但为了确保电脑端也能显示，我们默认显示按钮
  // if (window.deferredPrompt ||
  //     userAgent.indexOf("iPad") > -1 ||
  //     userAgent.indexOf("iPhone") > -1 ||
  //     userAgent.indexOf("iPod") > -1 ||
  //     userAgent.indexOf("Android") > -1) {
  //   showInstallButton.value = true;
  // }
};

// PWA 安装函数
const installPWA = () => {
  if (isIOS) {
    // iOS 设备显示手动安装提示
    appStore.setIosInfoShow(true);
  } else {
    // Android、电脑端和其他平台都使用 PWA 安装
    if (window.installButtonclick) {
      window.installButtonclick();
    } else {
      // 如果没有 installButtonclick 函数，显示提示信息
      console.log('PWA 安装功能不可用，请确保在支持的浏览器中打开');

      // 根据平台显示不同的提示信息
      const message = isDesktop
        ? '请在支持 PWA 的浏览器（如 Chrome、Edge）中打开此页面以安装应用到电脑'
        : '请在支持 PWA 的浏览器中打开此页面以安装应用';

      alert(message);
    }
  }
};
</script>

<template>
  <div class="app-index">
    <AppIndexHeader />
    <AppBanner class="app-banner-border" :list-data="[]" />
    <AppMiniBanner />
    <!-- <AppGameJackpot /> -->
<!-- 下载按钮· -->
    <!-- PWA 安装按钮 -->
    <div v-if="showInstallButton" class="pwa-install-container">
      <div class="pwa-install-button" @click="installPWA">
        <div class="pwa-install-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill="currentColor"/>
          </svg>
        </div>
        <div class="pwa-install-text">
          <div class="pwa-install-title">{{ installButtonText.title }}</div>
          <div class="pwa-install-subtitle">{{ installButtonText.subtitle }}</div>
        </div>
        <div class="pwa-install-close" @click.stop="showInstallButton = false">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </div>
      </div>
    </div>
    <!-- <AppGameMarquee /> -->
    <AppSpeaker />
    <AppScrollTab />
    <!--走马灯 -->
    <!-- <AppGameTab /> -->
    <!--类型选择-->
    <!-- <AppIndexMenu /> -->

    <!-- <AppGameContainer gameType="1" /> -->
    <div v-for="n in 7">
      <!-- {{  }} -->
      <AppGameContainer :gameType="n - 1" />
    </div>

    <div class="app-index-container">
      <!-- <RouterView /> -->
      <AppIndexFooter />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.app-index {
  // background-color: aqua;
  // padding-top: 110px;
  // @include webp('/icons/bg_pattern_tile');
  // background-image: url("/img/index/bg_pattern_tile.webp");
  // background-repeat: repeat;
  // background-size: 232px 232px;

  position: relative;
  overflow-x: hidden !important;
  overflow-y: auto;
}

.app-index-container {
  padding: 0px 20px 00px 20px;
}

// PWA 安装按钮样式
.pwa-install-container {
  margin: 16px 20px;
  animation: slideInDown 0.5s ease-out;
}

.pwa-install-button {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }
}

.pwa-install-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin-right: 12px;
  color: white;
}

.pwa-install-text {
  flex: 1;
  color: white;
}

.pwa-install-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.pwa-install-subtitle {
  font-size: 12px;
  opacity: 0.9;
}

.pwa-install-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
