<script setup lang="ts" name="home">
import AppGameJackpotNew from "~/components/AppGameJackpotNew.vue";

// 扩展 Window 接口
declare global {
  interface Window {
    deferredPrompt?: any;
    installButtonclick?: () => void;
  }
}

const appStore = useAppStore();
const { isLogin } = storeToRefs(appStore);

if (isLogin) {
  appStore.runGetMemberInfo();
}

appStore.setFooterDialogVisble(true);

// PWA 安装相关
const showInstallButton = ref(false);
const userAgent = navigator.userAgent || (navigator as any).vendor || '';

// 检测设备类型
const isIOS = userAgent.indexOf("iPad") > -1 || userAgent.indexOf("iPhone") > -1 || userAgent.indexOf("iPod") > -1;
const isAndroid = userAgent.indexOf("Android") > -1;
const isDesktop = !isIOS && !isAndroid;

// 根据设备类型显示不同的文本
const installButtonText = computed(() => {
  if (isIOS) {
    return {
      title: "安装应用",
      subtitle: "添加到主屏幕，获得更好体验"
    };
  } else if (isAndroid) {
    return {
      title: "安装应用",
      subtitle: "添加到主屏幕，获得更好体验"
    };
  } else {
    return {
      title: "安装应用",
      subtitle: "安装到电脑，获得更好体验"
    };
  }
});

onMounted(() => {
  const dom: any = document.querySelector(".app-index");

  dom.addEventListener("scroll", () => {
    if (dom.scrollTop > 200) {
      (document.querySelector(".app-index-go-top") as any).style.display =
        "flex";
    } else {
      (document.querySelector(".app-index-go-top") as any).style.display =
        "none";
    }
  });

  // 检查是否显示安装按钮
  checkInstallAvailability();
});

// 检查安装可用性
const checkInstallAvailability = () => {
  // 如果已经是 PWA 模式，不显示安装按钮
  if (window.matchMedia('(display-mode: standalone)').matches) {
    showInstallButton.value = false;
    console.log('应用已在 PWA 模式下运行，隐藏安装按钮');
    return;
  }

  // 检查浏览器是否支持 PWA
  const supportsPWA = 'serviceWorker' in navigator;

  if (!supportsPWA && !isIOS) {
    console.log('浏览器不支持 PWA 功能');
    showInstallButton.value = false;
    return;
  }

  // 始终显示安装按钮，让用户可以尝试安装
  showInstallButton.value = true;
  console.log('显示 PWA 安装按钮');

  // 延迟检查 beforeinstallprompt 事件
  setTimeout(() => {
    if (window.deferredPrompt) {
      console.log('PWA 安装提示可用');
    } else {
      console.log('PWA 安装提示暂不可用，可能需要满足更多条件');
    }
  }, 3000);
};

// PWA 安装函数
const installPWA = () => {
  console.log('用户点击安装按钮');

  if (isIOS) {
    // iOS 设备显示手动安装提示
    appStore.setIosInfoShow(true);
    return;
  }

  // 检查是否已经安装
  if (window.matchMedia('(display-mode: standalone)').matches) {
    alert('应用已经安装！您可以在桌面或应用列表中找到它。');
    return;
  }

  // 尝试使用 PWA 安装
  if (window.installButtonclick) {
    try {
      window.installButtonclick();
    } catch (error) {
      console.error('PWA 安装出错:', error);
      showManualInstallGuide();
    }
  } else {
    console.log('installButtonclick 函数不可用');
    showManualInstallGuide();
  }
};

// 显示手动安装指导
const showManualInstallGuide = () => {
  const userAgent = navigator.userAgent;
  let title = "安装应用到设备";
  let message = "";

  if (userAgent.indexOf("Chrome") > -1) {
    if (isDesktop) {
      message = `请按照以下步骤安装应用：

1. 点击浏览器地址栏右侧的"安装"图标 ⬇️
2. 或者点击浏览器菜单 ⋮ → "安装 cagadoslot"
3. 在弹出的对话框中点击"安装"

如果没有看到安装选项，请：
• 确保网站是通过 HTTPS 访问的
• 刷新页面后再试
• 多与页面交互一下再试`;
    } else {
      message = `请按照以下步骤安装应用：

1. 点击浏览器菜单 ⋮ (右上角三个点)
2. 选择"添加到主屏幕"或"安装应用"
3. 在弹出的对话框中点击"添加"或"安装"`;
    }
  } else if (userAgent.indexOf("Edge") > -1) {
    message = `请按照以下步骤安装应用：

1. 点击浏览器地址栏右侧的"应用"图标 📱
2. 或者点击浏览器菜单 ⋯ → "应用" → "安装此站点为应用"
3. 在弹出的对话框中点击"安装"`;
  } else if (userAgent.indexOf("Firefox") > -1) {
    message = `Firefox 浏览器目前对 PWA 的支持有限。

建议使用以下浏览器获得更好的安装体验：
• Google Chrome
• Microsoft Edge
• Safari (iOS/macOS)`;
  } else if (userAgent.indexOf("Safari") > -1) {
    if (isDesktop) {
      message = `Safari 浏览器目前对 PWA 的支持有限。

建议使用以下浏览器：
• Google Chrome
• Microsoft Edge`;
    } else {
      message = `请按照以下步骤添加到主屏幕：

1. 点击底部的分享按钮 📤
2. 向下滚动找到"添加到主屏幕"
3. 点击"添加"`;
    }
  } else {
    message = `当前浏览器可能不完全支持应用安装。

建议使用以下浏览器：
• Google Chrome
• Microsoft Edge
• Safari (移动设备)

或者尝试：
1. 查看浏览器地址栏是否有安装图标
2. 检查浏览器菜单中的"安装"或"添加到主屏幕"选项`;
  }

  // 使用更好的弹窗显示
  if (confirm(`${title}\n\n${message}\n\n点击"确定"了解更多，点击"取消"关闭此提示。`)) {
    // 可以在这里打开帮助页面或显示更多信息
    console.log('用户需要更多帮助');
  }
};
</script>

<template>
  <div class="app-index">
    <AppIndexHeader />
    <AppBanner class="app-banner-border" :list-data="[]" />
    <AppMiniBanner />
    <!-- <AppGameJackpot /> -->
<!-- 下载按钮· -->
    <!-- PWA 安装按钮 -->
    <div v-if="showInstallButton" class="pwa-install-container">
      <div class="pwa-install-button" @click="installPWA">
        <div class="pwa-install-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill="currentColor"/>
          </svg>
        </div>
        <div class="pwa-install-text">
          <div class="pwa-install-title">{{ installButtonText.title }}</div>
          <div class="pwa-install-subtitle">{{ installButtonText.subtitle }}</div>
        </div>
        <div class="pwa-install-close" @click.stop="showInstallButton = false">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </div>
      </div>
    </div>
    <!-- <AppGameMarquee /> -->
    <AppSpeaker />
    <AppScrollTab />
    <!--走马灯 -->
    <!-- <AppGameTab /> -->
    <!--类型选择-->
    <!-- <AppIndexMenu /> -->

    <!-- <AppGameContainer gameType="1" /> -->
    <div v-for="n in 7">
      <!-- {{  }} -->
      <AppGameContainer :gameType="n - 1" />
    </div>

    <div class="app-index-container">
      <!-- <RouterView /> -->
      <AppIndexFooter />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.app-index {
  // background-color: aqua;
  // padding-top: 110px;
  // @include webp('/icons/bg_pattern_tile');
  // background-image: url("/img/index/bg_pattern_tile.webp");
  // background-repeat: repeat;
  // background-size: 232px 232px;

  position: relative;
  overflow-x: hidden !important;
  overflow-y: auto;
}

.app-index-container {
  padding: 0px 20px 00px 20px;
}

// PWA 安装按钮样式
.pwa-install-container {
  margin: 16px 20px;
  animation: slideInDown 0.5s ease-out;
}

.pwa-install-button {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }
}

.pwa-install-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin-right: 12px;
  color: white;
}

.pwa-install-text {
  flex: 1;
  color: white;
}

.pwa-install-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.pwa-install-subtitle {
  font-size: 12px;
  opacity: 0.9;
}

.pwa-install-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
