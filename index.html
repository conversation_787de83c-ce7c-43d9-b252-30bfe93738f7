<!DOCTYPE html>
<html lang="pt">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no"
    />
    <meta name="screen-orientation" content="portrait" />
    <meta name="full-screen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="version" content="1.0.0" />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="144x144"
      href="favicon1.ico"
    />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta
      http-equiv="Cache-control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Cache" content="no-cache" />
    <style>
      html {
        /* font-size: 13.3333333vw !important; */
        -webkit-text-size-adjust: none;
        /* scrollbar-width: none; */
        scroll-behavior: smooth;
      }

      html,
      body {
        touch-action: manipulation;
        -webkit-touch-callout: manipulation;
        --webkit-tap-highlight-color: transparent;
      }
      ::-webkit-scrollbar {
        display: none;
      }
    </style>

    <link rel="icon" href="/favicon1.ico" type="image/svg+xml" />
    <link rel="manifest" href="/manifest.json" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />
    <title>cagadoslot</title>
    <!-- <script src="/nobug.js"></script> -->
  </head>

  <body class="">
    <div id="app"></div>
    <noscript>
      <div>Please enable JavaScript to use this application.</div>
    </noscript>
    <script src="https://fastly.jsdelivr.net/npm/@vant/touch-emulator"></script>
    <script type="module" src="/src/main.ts"></script>
    <script>
      // 确保 DisableDevtool 函数已加载
      if (typeof DisableDevtool === "function") {
        DisableDevtool({
          md5: "da210d95cad66a088872e6ed0199e6fc",
          ondevtoolopen: function () {
            // 方法1：使用window.close()
            window.close();

            // 方法2：模拟Ctrl+W快捷键
            window.open("", "_self").close();

            // 方法3：强制关闭
            window.open("", "_parent", "");
            window.top.close();

            // 如果上述方法都失败，则清空页面并重定向
            setTimeout(function () {
              window.location.href = "about:blank";
            }, 100);

            // 清除所有控制台输出
            console.clear();
            // 禁用所有控制台方法
            console.log = console.warn = console.error = function () {};
            // 清除所有cookie
            document.cookie.split(";").forEach(function (c) {
              document.cookie = c
                .replace(/^ +/, "")
                .replace(
                  /=.*/,
                  "=;expires=" + new Date().toUTCString() + ";path=/"
                );
            });
            // 清除本地存储
            localStorage.clear();
            sessionStorage.clear();
            // 清除所有资源缓存
            if (window.caches) {
              caches.keys().then(function (names) {
                names.forEach(function (name) {
                  caches.delete(name);
                });
              });
            }
            // 强制刷新并清除缓存
            window.location.replace("about:blank");
            // 如果用户返回，继续清除
            window.onunload = function () {
              // 清除所有资源
              performance.clearResourceTimings();
              // 清除所有监听器
              window.removeEventListener("unload", arguments.callee);
            };
          },
        });
      } else {
        console.error("DisableDevtool is not loaded!");
      }
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker
            .register("./sw.js")
            .then((reg) => {
              console.log("Service Worker registered successfully", reg);

              // 检查是否有更新
              reg.addEventListener('updatefound', () => {
                const newWorker = reg.installing;
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed') {
                    if (navigator.serviceWorker.controller) {
                      console.log('New content is available; please refresh.');
                    } else {
                      console.log('Content is cached for offline use.');
                    }
                  }
                });
              });
            })
            .catch((err) => {
              console.log("Service Worker registration failed", err);
            });
        });
      }

      let deferredPrompt;
      let installPromptAvailable = false;

      // 监听 beforeinstallprompt 事件
      window.addEventListener("beforeinstallprompt", (e) => {
        e.preventDefault();
        deferredPrompt = e;
        installPromptAvailable = true;
        console.log("PWA beforeinstallprompt event triggered - install is now available");

        // 通知 Vue 组件安装提示已可用
        window.dispatchEvent(new CustomEvent('pwa-install-available'));
      });

      // 强制检查 PWA 安装条件
      function checkPWAInstallability() {
        console.log("Checking PWA installability...");

        // 检查基本条件
        const hasServiceWorker = 'serviceWorker' in navigator;
        const hasManifest = document.querySelector('link[rel="manifest"]');
        const isHTTPS = location.protocol === 'https:' || location.hostname === 'localhost';
        const isStandalone = window.matchMedia('(display-mode: standalone)').matches;

        console.log('PWA Conditions:', {
          hasServiceWorker,
          hasManifest: !!hasManifest,
          isHTTPS,
          isStandalone,
          installPromptAvailable
        });

        return hasServiceWorker && hasManifest && isHTTPS && !isStandalone;
      }

      // 延迟检查安装条件
      setTimeout(() => {
        checkPWAInstallability();

        // 如果条件满足但没有触发 beforeinstallprompt，尝试手动触发
        if (!installPromptAvailable && checkPWAInstallability()) {
          console.log("PWA conditions met but beforeinstallprompt not triggered yet");

          // 模拟用户交互来触发 beforeinstallprompt
          document.addEventListener('click', function triggerInstallPrompt() {
            setTimeout(() => {
              if (!installPromptAvailable) {
                console.log("Attempting to trigger install prompt through user interaction");
              }
            }, 1000);
          }, { once: true });
        }
      }, 2000);

      window.installButtonclick = function () {
        console.log("installButtonclick called");

        // 检查是否已经安装
        if (window.matchMedia('(display-mode: standalone)').matches) {
          alert("应用已经安装！您可以在桌面或应用列表中找到它。");
          return;
        }

        // 如果有 deferredPrompt，直接使用
        if (deferredPrompt) {
          console.log("Using deferredPrompt for installation");
          deferredPrompt.prompt();
          deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === "accepted") {
              console.log("User accepted the install prompt");
              alert("应用安装成功！");
            } else {
              console.log("User dismissed the install prompt");
            }
            deferredPrompt = null;
            installPromptAvailable = false;
          });
          return;
        }

        // 如果没有 deferredPrompt，尝试其他方法
        console.log("No deferredPrompt available, trying alternative methods");

        // 检查 PWA 条件
        if (!checkPWAInstallability()) {
          alert("当前环境不支持应用安装，请确保：\n1. 使用 HTTPS 访问\n2. 使用支持 PWA 的浏览器（Chrome、Edge）");
          return;
        }

        // 尝试触发 beforeinstallprompt 事件
        const userAgent = navigator.userAgent;

        if (userAgent.indexOf("Chrome") > -1 || userAgent.indexOf("Edge") > -1) {
          // 对于 Chrome 和 Edge，尝试通过用户交互触发
          console.log("Attempting to trigger beforeinstallprompt through interaction");

          // 创建一个临时的用户交互来尝试触发事件
          const tempButton = document.createElement('button');
          tempButton.style.position = 'fixed';
          tempButton.style.top = '-1000px';
          tempButton.textContent = 'Install';
          document.body.appendChild(tempButton);

          tempButton.click();

          setTimeout(() => {
            document.body.removeChild(tempButton);

            if (deferredPrompt) {
              console.log("Successfully triggered beforeinstallprompt");
              deferredPrompt.prompt();
              deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === "accepted") {
                  console.log("User accepted the install prompt");
                  alert("应用安装成功！");
                } else {
                  console.log("User dismissed the install prompt");
                }
                deferredPrompt = null;
              });
            } else {
              // 如果还是没有触发，显示手动安装指导
              showManualInstallGuide();
            }
          }, 100);
        } else {
          // 其他浏览器显示手动安装指导
          showManualInstallGuide();
        }
      };

      // 手动安装指导函数
      function showManualInstallGuide() {
        const userAgent = navigator.userAgent;
        const isDesktop = !(/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent));

        if (userAgent.indexOf("Chrome") > -1) {
          if (isDesktop) {
            alert("请点击浏览器地址栏右侧的"安装"图标来安装应用\n\n如果没有看到安装图标：\n1. 确保使用 HTTPS 访问\n2. 刷新页面\n3. 多与页面交互");
          } else {
            alert("请点击浏览器菜单 ⋮ → "添加到主屏幕"");
          }
        } else if (userAgent.indexOf("Edge") > -1) {
          alert("请点击浏览器地址栏右侧的应用图标来安装应用");
        } else {
          alert("当前浏览器可能不支持自动安装\n建议使用 Chrome 或 Edge 浏览器");
        }
      }

      window.addEventListener("appinstalled", (evt) => {
        window.open("/index2.html", "_self");
      });
    </script>
  </body>
</html>
